import axios from 'axios';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
  loading?: boolean; // 加载状态
  thinking?: boolean; // 思考状态
  toolCalls?: ToolCall[]; // 工具调用信息
  status?: 'thinking' | 'calling_tools' | 'responding' | 'complete'; // 处理状态
}

export interface ToolCall {
  name: string;
  args: any;
  result?: string;
  status?: 'calling' | 'success' | 'error';
}

export interface ChatRequest {
  message: string;
  thread_id?: string;
  stream?: boolean;
}

export interface ChatResponse {
  message: string;
  thread_id: string;
  timestamp: string;
}

export interface SessionInfo {
  thread_id: string;
  title: string;
  created_at: string;
  last_active_time: string;
  message_count: number;
}

export interface CreateSessionRequest {
  title?: string;
}

export interface UpdateSessionRequest {
  title: string;
}

// === 第二阶段新增接口 ===

// 会话恢复相关接口
export interface SessionContextRecoveryRequest {
  session_id: string;
}

export interface SessionContextRecoveryResponse {
  success: boolean;
  session_id: string;
  message_count?: number;
  langgraph_message_count?: number;
  recovery_time?: string;
  session_info?: any;
  recovery_details?: any;
  error?: string;
}

export interface RecoverableSession {
  session_id: string;
  title: string;
  created_at: string;
  last_activity: string;
  message_count: number;
  can_recover: boolean;
  recovery_score: number;
}

export interface RecoverableSessionsResponse {
  sessions: RecoverableSession[];
  total: number;
  user_id: string;
}

// 会话完整性检查接口
export interface SessionIntegrityResponse {
  valid: boolean;
  session_id: string;
  metadata_exists?: boolean;
  message_count?: number;
  langgraph_message_count?: number;
  state_consistent?: boolean;
  session_info?: any;
  error?: string;
}

// 监控相关接口
export interface ActiveSessionsStats {
  active_sessions: number;
  active_users: number;
  activity_rate: number;
  time_window_hours: number;
  recent_activity: any[];
}

export interface SessionDurationAnalysis {
  total_sessions: number;
  sessions_with_messages: number;
  avg_duration_minutes: number;
  max_duration_minutes: number;
  duration_distribution: any;
  top_longest_sessions: any[];
}

export interface MessageFrequencyStats {
  total_messages: number;
  daily_average: number;
  frequency_by_date: any[];
  active_sessions_trend: any[];
  peak_activity_hours: any[];
}

export interface UserActivityAnalysis {
  total_users: number;
  avg_sessions_per_user: number;
  avg_messages_per_user: number;
  most_active_users: any[];
  user_engagement_distribution: any;
}

export interface SystemHealthMetrics {
  status: string;
  database_size_mb: number;
  table_status: any;
  recent_activity: any;
  performance_metrics: any;
}

// 移除文件上传相关接口 - 只使用MateChat官方组件

export class ChatAPI {
  private baseURL = 'http://localhost:8000';
  private client = axios.create({
    baseURL: this.baseURL,
    timeout: 30000,
  });

  // 发送聊天消息
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const response = await this.client.post('/api/chat', request);
    return response.data;
  }

  // SSE流式聊天
  async sendStreamMessage(
    request: ChatRequest,
    onChunk: (chunk: any) => void,
    onComplete?: () => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/api/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('No response body');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            onComplete?.();
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保留最后一行（可能不完整）

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                onChunk(data);
              } catch (e) {
                console.warn('Failed to parse SSE data:', line);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      onError?.(error as Error);
      throw error;
    }
  }

  // WebSocket连接
  connectWebSocket(threadId: string): WebSocket {
    return new WebSocket(`ws://localhost:8000/ws/${threadId}`);
  }

  // 获取会话历史
  async getSessionHistory(threadId: string): Promise<ChatMessage[]> {
    const response = await this.client.get(`/api/sessions/${threadId}/history`);
    return response.data.history || [];
  }

  // 获取可用工具
  async getAvailableTools(): Promise<any[]> {
    const response = await this.client.get('/api/tools');
    return response.data.tools || [];
  }

  // 会话管理API
  async createSession(request: CreateSessionRequest): Promise<SessionInfo> {
    const response = await this.client.post('/api/sessions', request);
    return response.data;
  }

  async listSessions(userId?: string): Promise<SessionInfo[]> {
    const params = userId ? { user_id: userId } : {};
    const response = await this.client.get('/api/sessions', { params });
    return response.data.sessions || [];
  }

  // 获取会话列表 - 兼容方法
  async getSessions(userId?: string): Promise<{ sessions: SessionInfo[] }> {
    const sessions = await this.listSessions(userId);
    return { sessions };
  }

  // 获取会话消息
  async getSessionMessages(sessionId: string): Promise<ChatMessage[]> {
    const response = await this.client.get(`/api/sessions/${sessionId}/messages`);
    return response.data.messages || [];
  }

  async updateSession(threadId: string, request: UpdateSessionRequest): Promise<any> {
    const response = await this.client.put(`/api/sessions/${threadId}`, request);
    return response.data;
  }

  async deleteSession(threadId: string): Promise<any> {
    const response = await this.client.delete(`/api/sessions/${threadId}`);
    return response.data;
  }

  // === 第二阶段新增API方法 ===

  // 会话恢复相关API
  async restoreSessionContext(sessionId: string): Promise<SessionContextRecoveryResponse> {
    const response = await this.client.post(`/api/sessions/${sessionId}/restore`);
    return response.data;
  }

  async getRecoverableSessions(userId: string, limit: number = 20): Promise<RecoverableSessionsResponse> {
    const response = await this.client.get(`/api/sessions/recoverable/${userId}`, {
      params: { limit }
    });
    return response.data;
  }

  async checkSessionIntegrity(sessionId: string): Promise<SessionIntegrityResponse> {
    const response = await this.client.get(`/api/sessions/${sessionId}/integrity`);
    return response.data;
  }

  // 监控相关API
  async getActiveSessionsStats(timeWindowHours: number = 24): Promise<ActiveSessionsStats> {
    const response = await this.client.get('/api/sessions/monitoring/active-sessions', {
      params: { time_window_hours: timeWindowHours }
    });
    return response.data.data;
  }

  async getSessionDurationAnalysis(limit: number = 100): Promise<SessionDurationAnalysis> {
    const response = await this.client.get('/api/sessions/monitoring/session-duration', {
      params: { limit }
    });
    return response.data.data;
  }

  async getMessageFrequencyStats(days: number = 7): Promise<MessageFrequencyStats> {
    const response = await this.client.get('/api/sessions/monitoring/message-frequency', {
      params: { days }
    });
    return response.data.data;
  }

  async getUserActivityAnalysis(limit: number = 50): Promise<UserActivityAnalysis> {
    const response = await this.client.get('/api/sessions/monitoring/user-activity', {
      params: { limit }
    });
    return response.data.data;
  }

  async getSystemHealthMetrics(): Promise<SystemHealthMetrics> {
    const response = await this.client.get('/api/sessions/monitoring/system-health');
    return response.data.data;
  }

  // 移除文件上传API - 只使用MateChat官方组件
}

// 流式响应处理类
export class StreamingService {
  private baseURL = 'http://localhost:8000';

  // 创建流式聊天连接
  async createStreamingChat(
    request: ChatRequest,
    callbacks: {
      onStart?: () => void;
      onChunk?: (content: string, isComplete: boolean) => void;
      onComplete?: (fullResponse: string) => void;
      onError?: (error: Error) => void;
    }
  ): Promise<void> {
    let fullResponse = '';
    let currentMessage = '';

    try {
      callbacks.onStart?.();

      await chatAPI.sendStreamMessage(
        request,
        (chunk) => {
          // 处理不同类型的流式数据
          if (chunk.type === 'AIMessage' || chunk.type === 'ai_chunk') {
            const content = chunk.content || '';
            if (content) {
              currentMessage += content;
              fullResponse += content;
              callbacks.onChunk?.(currentMessage, false);
            }
          } else if (chunk.type === 'Error') {
            callbacks.onError?.(new Error(chunk.content || '处理消息时出现错误'));
          }
        },
        () => {
          // 流式响应完成
          callbacks.onChunk?.(currentMessage, true);
          callbacks.onComplete?.(fullResponse);
        },
        (error) => {
          callbacks.onError?.(error);
        }
      );
    } catch (error) {
      callbacks.onError?.(error as Error);
    }
  }

  // 打字机效果显示
  async displayWithTypewriter(
    text: string,
    callback: (displayText: string, isComplete: boolean) => void,
    speed: number = 30 // 每个字符的显示间隔（毫秒）
  ): Promise<void> {
    let displayText = '';

    for (let i = 0; i < text.length; i++) {
      displayText += text[i];
      callback(displayText, i === text.length - 1);

      if (i < text.length - 1) {
        await new Promise(resolve => setTimeout(resolve, speed));
      }
    }
  }
}

export const chatAPI = new ChatAPI();
export const streamingService = new StreamingService();
