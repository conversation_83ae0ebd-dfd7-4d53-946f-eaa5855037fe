"""
简化的会话命名服务 - 基于官方最佳实践
参考LangGraph和MateChat官方的简单命名方式
"""

from datetime import datetime


class SessionNamingService:
    """
    简化的会话命名服务
    基于用户消息的简单规则生成标题
    """

    def __init__(self, max_title_length: int = 20):
        self.max_title_length = max_title_length

    def generate_simple_title(self, user_message: str) -> str:
        """
        基于用户消息生成简单标题（简化版）

        Args:
            user_message: 用户的消息内容

        Returns:
            生成的简单标题
        """
        # 清理消息内容
        content = user_message.strip()

        # 如果消息为空，使用时间戳
        if not content:
            return f"对话 {datetime.now().strftime('%m-%d %H:%M')}"

        # 取前20个字符作为标题
        if len(content) > self.max_title_length:
            title = content[:self.max_title_length - 3] + "..."
        else:
            title = content

        # 移除换行符和多余空格
        title = " ".join(title.split())

        return title or f"对话 {datetime.now().strftime('%m-%d %H:%M')}"

    # 兼容性方法（保持API一致性）
    async def generate_intelligent_title(self, first_message: str,
                                       fallback_format: str = "对话 {time}") -> str:
        """兼容性方法，调用简化的标题生成"""
        return self.generate_simple_title(first_message)


# 全局实例
_naming_service = None


def get_naming_service() -> SessionNamingService:
    """获取命名服务实例（单例模式）"""
    global _naming_service
    if _naming_service is None:
        _naming_service = SessionNamingService()
    return _naming_service
