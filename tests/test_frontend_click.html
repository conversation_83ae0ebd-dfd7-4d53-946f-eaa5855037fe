<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端点击测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .session-item {
            padding: 10px;
            margin: 5px 0;
            background: #f5f5f5;
            border-radius: 3px;
            cursor: pointer;
        }
        .session-item:hover {
            background: #e0e0e0;
        }
        .log {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🧪 前端会话点击测试</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>这个页面用于测试前端会话点击功能。请按以下步骤操作：</p>
        <ol>
            <li>打开浏览器开发者工具 (F12)</li>
            <li>切换到 Console 标签页</li>
            <li>点击下面的"获取会话列表"按钮</li>
            <li>点击任意会话项</li>
            <li>观察控制台输出</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔧 控制面板</h2>
        <button class="button" onclick="loadSessions()">获取会话列表</button>
        <button class="button" onclick="clearLog()">清空日志</button>
        <button class="button" onclick="testDirectAPI()">直接测试API</button>
    </div>

    <div class="test-section">
        <h2>📝 会话列表</h2>
        <div id="sessions-container">
            <p>点击"获取会话列表"按钮加载会话...</p>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 测试日志</h2>
        <div id="log" class="log">等待测试开始...</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        async function loadSessions() {
            log('🔄 开始获取会话列表...');
            
            try {
                const response = await fetch(`${API_BASE}/api/sessions/`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                const sessions = data.sessions || [];
                
                log(`✅ 成功获取 ${sessions.length} 个会话`);
                
                // 显示会话列表
                const container = document.getElementById('sessions-container');
                container.innerHTML = '';
                
                if (sessions.length === 0) {
                    container.innerHTML = '<p>没有找到会话</p>';
                    return;
                }
                
                sessions.forEach((session, index) => {
                    const sessionDiv = document.createElement('div');
                    sessionDiv.className = 'session-item';
                    sessionDiv.innerHTML = `
                        <strong>${session.title}</strong><br>
                        <small>ID: ${session.id}</small><br>
                        <small>消息数: ${session.message_count || 0}</small>
                    `;
                    
                    sessionDiv.onclick = () => testSessionClick(session);
                    container.appendChild(sessionDiv);
                });
                
            } catch (error) {
                log(`❌ 获取会话列表失败: ${error.message}`);
            }
        }
        
        async function testSessionClick(session) {
            log(`🖱️ 点击会话: ${session.title}`);
            log(`   会话ID: ${session.id}`);
            
            try {
                // 模拟前端的会话切换逻辑
                log('🔄 模拟前端会话切换...');
                
                // 1. 获取会话消息
                const response = await fetch(`${API_BASE}/api/sessions/${session.id}/messages`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const messages = await response.json();
                log(`✅ 成功获取 ${messages.length} 条消息`);
                
                if (messages.length > 0) {
                    log(`   第一条消息: ${messages[0].role} - ${messages[0].content.substring(0, 50)}...`);
                }
                
                // 2. 模拟状态更新
                log('🎯 模拟状态更新完成');
                log('✅ 会话切换测试完成');
                
            } catch (error) {
                log(`❌ 会话切换测试失败: ${error.message}`);
            }
        }
        
        async function testDirectAPI() {
            log('🧪 直接测试API连接...');
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                log(`✅ API连接正常: ${JSON.stringify(data)}`);
            } catch (error) {
                log(`❌ API连接失败: ${error.message}`);
            }
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            log('📱 测试页面已加载');
            log('💡 请打开开发者工具查看详细日志');
        };
    </script>
</body>
</html>
