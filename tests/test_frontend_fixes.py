#!/usr/bin/env python3
"""
前端修复测试脚本
测试历史会话加载和消息对齐修复
"""

import asyncio
import aiohttp
import json

BASE_URL = "http://localhost:8000"

async def test_session_history_api():
    """测试历史会话API"""
    print("🧪 测试历史会话API...")
    
    async with aiohttp.ClientSession() as session:
        # 获取会话列表
        async with session.get(f"{BASE_URL}/api/sessions/") as resp:
            if resp.status == 200:
                data = await resp.json()
                sessions = data.get('sessions', [])
                
                if sessions:
                    session_id = sessions[0]['id']
                    print(f"✅ 找到会话: {session_id}")
                    
                    # 获取会话消息
                    async with session.get(f"{BASE_URL}/api/sessions/{session_id}/messages") as msg_resp:
                        if msg_resp.status == 200:
                            messages_data = await msg_resp.json()
                            messages = messages_data.get('messages', [])
                            print(f"✅ 成功加载 {len(messages)} 条历史消息")
                            
                            if messages:
                                # 显示消息示例
                                for i, msg in enumerate(messages[:2]):  # 只显示前2条
                                    print(f"   消息 {i+1}: {msg['role']} - {msg['content'][:50]}...")
                                return True
                            else:
                                print("📝 该会话暂无历史消息")
                                return True
                        else:
                            print(f"❌ 获取消息失败: {msg_resp.status}")
                else:
                    print("📝 暂无会话")
                    return True
            else:
                print(f"❌ 获取会话列表失败: {resp.status}")
    
    return False

async def test_create_test_session():
    """创建测试会话"""
    print("🧪 创建测试会话...")
    
    async with aiohttp.ClientSession() as session:
        # 发送测试消息
        payload = {
            "message": "测试历史会话加载功能",
            "user_id": "test_frontend_fix"
        }
        
        async with session.post(f"{BASE_URL}/api/chat", json=payload) as resp:
            if resp.status == 200:
                result = await resp.json()
                print(f"✅ 创建测试会话成功")
                return True
            else:
                print(f"❌ 创建测试会话失败: {resp.status}")
    
    return False

async def main():
    """主测试函数"""
    print("🚀 开始前端修复测试")
    print("=" * 50)
    
    # 创建测试会话
    create_success = await test_create_test_session()
    if not create_success:
        print("❌ 创建测试会话失败")
        return
    
    print()
    
    # 测试历史会话API
    history_success = await test_session_history_api()
    if not history_success:
        print("❌ 历史会话API测试失败")
        return
    
    print()
    print("=" * 50)
    print("🎉 前端修复测试通过！")
    print("✅ 历史会话API正常工作")
    print("✅ 消息格式正确")
    print("📝 前端修复内容:")
    print("   1. switchToSession 方法改为异步，从服务器加载历史消息")
    print("   2. 全局CSS text-align 从 center 改为 left，修复AI消息对齐")
    print("   3. selectSession 方法改为异步，支持历史消息加载")

if __name__ == "__main__":
    asyncio.run(main())
