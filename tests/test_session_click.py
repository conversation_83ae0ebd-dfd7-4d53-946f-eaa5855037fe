#!/usr/bin/env python3
"""
测试前端会话点击功能
"""

import asyncio
import aiohttp
import json

async def test_session_click():
    """测试会话点击功能"""
    print("🚀 开始测试会话点击功能")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 1. 获取会话列表
        print("🧪 获取会话列表...")
        async with session.get('http://localhost:8000/api/sessions/') as resp:
            if resp.status != 200:
                print(f"❌ 获取会话列表失败: {resp.status}")
                return
            
            data = await resp.json()
            sessions = data.get('sessions', [])
            
            if not sessions:
                print("❌ 没有找到任何会话")
                return
            
            print(f"✅ 找到 {len(sessions)} 个会话")
            
            # 显示会话列表
            for i, sess in enumerate(sessions[:5]):  # 只显示前5个
                print(f"   会话 {i+1}: {sess['title']} (ID: {sess['id'][:8]}...)")
        
        # 2. 选择一个会话进行测试
        test_session = sessions[0]
        session_id = test_session['id']
        session_title = test_session['title']
        
        print(f"\n🧪 测试点击会话: '{session_title}'")
        print(f"   会话ID: {session_id}")
        
        # 3. 获取该会话的消息
        print("🔄 获取会话消息...")
        async with session.get(f'http://localhost:8000/api/sessions/{session_id}/messages') as resp:
            if resp.status != 200:
                print(f"❌ 获取会话消息失败: {resp.status}")
                return
            
            messages_data = await resp.json()
            messages = messages_data if isinstance(messages_data, list) else messages_data.get('messages', [])
            print(f"✅ 成功获取 {len(messages)} 条消息")

            if messages:
                print("   前3条消息:")
                for i, msg in enumerate(messages[:3]):
                    content_preview = msg['content'][:50] + "..." if len(msg['content']) > 50 else msg['content']
                    print(f"     {i+1}. {msg['role']}: {content_preview}")
            else:
                print("   该会话暂无消息")
        
        # 4. 测试会话切换的API调用流程
        print(f"\n🧪 模拟前端会话切换流程...")
        
        # 模拟前端的 selectSession -> switchToSession 调用
        print(f"   1. 调用 selectSession('{session_id}')")
        print(f"   2. 内部调用 switchToSession('{session_id}')")
        print(f"   3. 设置 currentSessionId = '{session_id}'")
        print(f"   4. 设置 currentThreadId = '{session_id}'")
        print(f"   5. 加载本地缓存消息")
        print(f"   6. 从服务器获取最新消息")
        
        # 验证会话是否存在
        session_exists = any(s['id'] == session_id for s in sessions)
        print(f"   ✅ 会话存在性验证: {'通过' if session_exists else '失败'}")
        
        # 验证消息API
        message_api_works = len(messages) >= 0  # 即使没有消息也是正常的
        print(f"   ✅ 消息API验证: {'通过' if message_api_works else '失败'}")
        
    print("\n" + "=" * 50)
    print("🎉 会话点击功能测试完成！")
    print("\n📝 如果前端点击没有响应，可能的原因:")
    print("   1. JavaScript控制台有错误")
    print("   2. Vue组件状态更新问题")
    print("   3. 事件处理器绑定问题")
    print("   4. 异步操作未正确等待")
    print("\n💡 建议检查:")
    print("   1. 打开浏览器开发者工具 -> Console")
    print("   2. 点击会话时查看是否有错误信息")
    print("   3. 检查Network标签页是否有API请求")
    print("   4. 检查Vue DevTools中的状态变化")

if __name__ == "__main__":
    asyncio.run(test_session_click())
