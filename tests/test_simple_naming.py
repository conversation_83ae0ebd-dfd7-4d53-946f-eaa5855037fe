#!/usr/bin/env python3
"""
简单的智能命名测试
直接测试WebSocket通信和智能命名功能
"""

import asyncio
import websockets
import json
import aiohttp
import sys

async def test_simple_naming():
    """简单测试智能命名"""
    print("🚀 开始简单智能命名测试")
    
    try:
        # 1. 创建新会话
        async with aiohttp.ClientSession() as session:
            async with session.post('http://localhost:8000/api/sessions/', 
                                   json={"title": "新对话", "user_id": "simple_test"}) as resp:
                if resp.status == 200:
                    session_data = await resp.json()
                    session_id = session_data.get('id')
                    print(f"✅ 会话创建成功: {session_id}")
                    print(f"   初始标题: '{session_data.get('title')}'")
                else:
                    print(f"❌ 创建会话失败: {resp.status}")
                    return False
        
        # 2. 连接WebSocket并发送消息
        uri = f'ws://localhost:8000/ws/{session_id}'
        print(f"🔗 连接WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 等待连接确认
            response = await websocket.recv()
            print(f"📨 连接确认: {json.loads(response)}")
            
            # 发送测试消息
            test_message = {
                'type': 'chat',
                'message': '请解释一下什么是人工智能',
                'thread_id': session_id,
                'user_id': 'simple_test'
            }
            
            await websocket.send(json.dumps(test_message))
            print(f"📤 发送消息: {test_message['message']}")
            
            # 监听所有响应
            responses = []
            title_updated = False
            complete_received = False

            try:
                while True:
                    response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    data = json.loads(response)
                    responses.append(data)

                    print(f"📨 收到响应: type={data.get('type')}, status={data.get('status')}")

                    if data.get('type') == 'session_title_updated':
                        title_updated = True
                        print(f"🏷️ 标题更新通知: '{data.get('title')}'")

                    if data.get('status') == 'complete':
                        print("✅ AI响应完成")
                        complete_received = True
                        # 继续等待可能的标题更新通知
                        continue

                    # 如果已经收到complete且收到了标题更新，可以退出
                    if complete_received and title_updated:
                        print("🎉 收到完整响应和标题更新")
                        break

            except asyncio.TimeoutError:
                print("⏰ 响应超时")
                if complete_received:
                    print("✅ AI响应已完成，但可能未收到标题更新通知")
        
        # 3. 检查最终会话状态
        async with aiohttp.ClientSession() as session:
            async with session.get(f'http://localhost:8000/api/sessions/{session_id}') as resp:
                if resp.status == 200:
                    final_session = await resp.json()
                    final_title = final_session.get('title')
                    message_count = final_session.get('message_count', 0)
                    
                    print(f"📋 最终会话状态:")
                    print(f"   标题: '{final_title}'")
                    print(f"   消息数: {message_count}")
                    
                    # 判断是否成功
                    if title_updated and final_title != "新对话":
                        print("🎉 智能命名成功！")
                        return True
                    else:
                        print("❌ 智能命名失败")
                        print(f"   标题更新通知: {title_updated}")
                        print(f"   最终标题: '{final_title}'")
                        return False
                else:
                    print(f"❌ 获取会话状态失败: {resp.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    result = await test_simple_naming()
    return result

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("\n🎯 测试通过")
            sys.exit(0)
        else:
            print("\n💥 测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        sys.exit(1)
