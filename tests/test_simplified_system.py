#!/usr/bin/env python3
"""
简化系统测试脚本
测试删除逻辑和命名逻辑的简化是否正常工作
"""

import asyncio
import aiohttp
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

async def test_simplified_naming():
    """测试简化的命名功能"""
    print("🧪 测试简化的命名功能...")
    
    async with aiohttp.ClientSession() as session:
        # 测试长消息的标题生成
        long_message = "请帮我写一个非常复杂的Python函数，用于处理大规模数据分析和机器学习任务，包括数据预处理、特征工程、模型训练和评估"
        
        payload = {
            "message": long_message,
            "user_id": "test_simplified"
        }
        
        async with session.post(f"{BASE_URL}/api/chat", json=payload) as resp:
            if resp.status == 200:
                result = await resp.json()
                print(f"✅ 聊天请求成功")
                
                # 获取会话列表，检查标题
                async with session.get(f"{BASE_URL}/api/sessions/?user_id=test_simplified") as sess_resp:
                    if sess_resp.status == 200:
                        sessions = await sess_resp.json()
                        if sessions['sessions']:
                            latest_session = sessions['sessions'][0]
                            title = latest_session['title']
                            print(f"✅ 生成的标题: '{title}'")
                            
                            # 验证标题是否符合简化规则（前20个字符+省略号）
                            if len(title) <= 23 and title.endswith('...'):
                                print("✅ 标题长度符合简化规则")
                                return latest_session['id']
                            else:
                                print(f"❌ 标题不符合简化规则: 长度={len(title)}, 内容='{title}'")
                        else:
                            print("❌ 没有找到会话")
                    else:
                        print(f"❌ 获取会话列表失败: {sess_resp.status}")
            else:
                print(f"❌ 聊天请求失败: {resp.status}")
    
    return None

async def test_simplified_deletion(session_id):
    """测试简化的删除功能"""
    print("🧪 测试简化的删除功能...")
    
    async with aiohttp.ClientSession() as session:
        # 删除会话
        async with session.delete(f"{BASE_URL}/api/sessions/{session_id}") as resp:
            if resp.status == 200:
                result = await resp.json()
                print(f"✅ 删除请求成功")
                print(f"   删除类型: {result.get('deletion_type')}")
                print(f"   可恢复: {result.get('can_recover')}")
                print(f"   消息: {result.get('message')}")
                
                # 验证是否为直接删除
                if result.get('deletion_type') == 'direct' and not result.get('can_recover'):
                    print("✅ 确认为简化的直接删除")
                    
                    # 验证会话确实被删除
                    async with session.get(f"{BASE_URL}/api/sessions/{session_id}") as check_resp:
                        if check_resp.status == 404:
                            print("✅ 会话已被彻底删除")
                            return True
                        else:
                            print(f"❌ 会话仍然存在: {check_resp.status}")
                else:
                    print(f"❌ 删除类型不正确: {result}")
            else:
                print(f"❌ 删除请求失败: {resp.status}")
    
    return False

async def test_mcp_tools():
    """测试MCP工具是否正常工作"""
    print("🧪 测试MCP工具...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/tools") as resp:
            if resp.status == 200:
                result = await resp.json()
                tools = result.get('tools', [])
                tool_count = len(tools)
                print(f"✅ 成功加载 {tool_count} 个MCP工具")
                
                # 检查关键工具
                tool_names = [tool['name'] for tool in tools]
                expected_tools = ['sequentialthinking', 'tavily-search', 'generate_bar_chart']
                
                for tool_name in expected_tools:
                    if tool_name in tool_names:
                        print(f"✅ 找到工具: {tool_name}")
                    else:
                        print(f"❌ 缺少工具: {tool_name}")
                
                return tool_count >= 25  # 期望至少25个工具
            else:
                print(f"❌ 获取工具列表失败: {resp.status}")
    
    return False

async def main():
    """主测试函数"""
    print("🚀 开始简化系统测试")
    print("=" * 50)
    
    # 测试命名功能
    session_id = await test_simplified_naming()
    if not session_id:
        print("❌ 命名功能测试失败")
        return
    
    print()
    
    # 测试删除功能
    deletion_success = await test_simplified_deletion(session_id)
    if not deletion_success:
        print("❌ 删除功能测试失败")
        return
    
    print()
    
    # 测试MCP工具
    tools_success = await test_mcp_tools()
    if not tools_success:
        print("❌ MCP工具测试失败")
        return
    
    print()
    print("=" * 50)
    print("🎉 所有简化系统测试通过！")
    print("✅ 删除逻辑已简化为直接删除")
    print("✅ 命名逻辑已简化为前20字符+省略号")
    print("✅ MCP工具正常工作")
    print("✅ 系统整体运行正常")

if __name__ == "__main__":
    asyncio.run(main())
