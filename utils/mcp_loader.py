# mcp_loader.py
import json
from langchain_mcp_adapters.client import MultiServerMCPClient
from typing import List
from langchain_core.tools import BaseTool

async def load_mcp_tools_from_config(config_path: str = "mcp_config.json"):
    """
    从指定的JSON配置文件加载并初始化MCP工具。

    Args:
        config_path (str): MCP工具配置文件的路径。

    Returns:
        Tuple[MultiServerMCPClient, List[BaseTool]]: MCP客户端和LangChain兼容的工具列表。
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print(f"错误: 配置文件 '{config_path}' 未找到。")
        exit(1)
    except json.JSONDecodeError:
        print(f"错误: 无法解析配置文件 '{config_path}'。请检查其JSON格式。")
        exit(1)

    print(f"正在从 '{config_path}' 加载MCP工具...")

    # 检查是否有MCP服务器配置
    if not config.get("mcpServers"):
        print("警告: 没有配置MCP服务器，将使用空工具列表")
        return None, []

    try:
        # 使用 MultiServerMCPClient 从配置字典创建客户端，并获取工具
        print(f"正在连接 {len(config['mcpServers'])} 个MCP服务器...")
        for server_name, server_config in config["mcpServers"].items():
            print(f"  - 服务器: {server_name}")
            if server_config.get("transport") == "stdio":
                print(f"    命令: {server_config.get('command')} {' '.join(server_config.get('args', []))}")
            elif server_config.get("transport") == "sse":
                print(f"    SSE URL: {server_config.get('url')}")

        client = MultiServerMCPClient(config["mcpServers"])
        tools = await client.get_tools()

        print(f"成功加载 {len(tools)} 个MCP工具:")
        for tool in tools:
            print(f"  - 工具名称: {tool.name}")
            print(f"    描述: {tool.description[:100]}...")

        return client, tools
    except Exception as e:
        print(f"错误: MCP工具加载失败")
        print(f"详细错误信息: {str(e)}")
        print(f"错误类型: {type(e).__name__}")

        # 尝试逐个测试服务器连接
        print("\n正在逐个测试MCP服务器连接...")
        working_servers = {}

        for server_name, server_config in config["mcpServers"].items():
            try:
                print(f"\n测试服务器: {server_name}")
                single_client = MultiServerMCPClient({server_name: server_config})
                single_tools = await single_client.get_tools()
                print(f"  ✅ {server_name}: 成功连接，获得 {len(single_tools)} 个工具")
                working_servers[server_name] = server_config
            except Exception as server_error:
                print(f"  ❌ {server_name}: 连接失败")
                print(f"     错误: {str(server_error)}")
                print(f"     类型: {type(server_error).__name__}")

        if working_servers:
            print(f"\n尝试使用 {len(working_servers)} 个可用服务器...")
            try:
                client = MultiServerMCPClient(working_servers)
                tools = await client.get_tools()
                print(f"成功加载 {len(tools)} 个MCP工具（部分服务器）")
                return client, tools
            except Exception as partial_error:
                print(f"部分服务器加载也失败: {partial_error}")

        print("所有MCP服务器连接失败，系统将继续运行但不包含MCP工具")
        return None, []